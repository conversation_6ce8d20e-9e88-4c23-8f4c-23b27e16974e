<template>
  <div class="consult-management">
    <div class="page-header">
      <h2>咨询管理</h2>
      <p>管理专家咨询信息，查看用户提交的咨询内容</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="专家姓名">
            <el-input
              v-model="searchForm.expertName"
              placeholder="请输入专家姓名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option label="待回复" value="pending" />
              <el-option label="已回复" value="replied" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索标题或内容"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading"> 搜索 </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total }}</div>
              <div class="stat-label">总咨询数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-number">{{ stats.pending }}</div>
              <div class="stat-label">待回复</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card replied">
            <div class="stat-content">
              <div class="stat-number">{{ stats.replied }}</div>
              <div class="stat-label">已回复</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card closed">
            <div class="stat-content">
              <div class="stat-number">{{ stats.closed }}</div>
              <div class="stat-label">已关闭</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 咨询列表 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>咨询列表</span>
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>

        <el-table :data="consultationList" v-loading="loading" stripe style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="title" label="咨询标题" min-width="200">
            <template #default="{ row }">
              <el-link type="primary" @click="viewDetail(row)">
                {{ row.title }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="expertName" label="咨询专家" width="120" />
          <el-table-column prop="contact" label="联系方式" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submitTime" label="提交时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.submitTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewDetail(row)"> 查看 </el-button>
              <el-button
                v-if="row.status === 'pending'"
                type="success"
                size="small"
                @click="replyConsult(row)"
              >
                回复
              </el-button>
              <el-button type="danger" size="small" @click="deleteConsult(row)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 详情弹框 -->
    <ConsultDetailDialog
      v-model="detailVisible"
      :consultation="selectedConsultation"
      @refresh="refreshData"
      @reply="handleReplyFromDetail"
    />

    <!-- 回复弹框 -->
    <ConsultReplyDialog
      v-model="replyVisible"
      :consultation="selectedConsultation"
      @refresh="refreshData"
    />
  </div>
</template>

<script>
import { getConsultations, getConsultationStats, deleteConsultation } from './consultDatabase.js'
import ConsultDetailDialog from './consultDetailDialog.vue'
import ConsultReplyDialog from './consultReplyDialog.vue'
import { Refresh } from '@element-plus/icons-vue'

export default {
  name: 'ConsultManagement',
  components: {
    ConsultDetailDialog,
    ConsultReplyDialog,
    Refresh
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        expertName: '',
        status: '',
        keyword: ''
      },
      // 分页信息
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 数据
      consultationList: [],
      stats: {
        total: 0,
        pending: 0,
        replied: 0,
        closed: 0
      },
      // 状态
      loading: false,
      detailVisible: false,
      replyVisible: false,
      selectedConsultation: null
    }
  },
  mounted() {
    this.loadData()
    this.loadStats()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }

        const result = await getConsultations(params)
        if (result.success) {
          this.consultationList = result.data.list
          this.pagination.total = result.data.total
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 加载统计信息
    async loadStats() {
      try {
        const result = await getConsultationStats()
        if (result.success) {
          this.stats = result.data
        }
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        expertName: '',
        status: '',
        keyword: ''
      }
      this.pagination.page = 1
      this.loadData()
    },

    // 刷新数据
    refreshData() {
      this.loadData()
      this.loadStats()
    },

    // 查看详情
    viewDetail(row) {
      this.selectedConsultation = row
      this.detailVisible = true
    },

    // 回复咨询
    replyConsult(row) {
      this.selectedConsultation = row
      this.replyVisible = true
    },

    // 从详情页面回复
    handleReplyFromDetail(consultation) {
      this.detailVisible = false
      this.selectedConsultation = consultation
      this.replyVisible = true
    },

    // 删除咨询
    async deleteConsult(row) {
      try {
        await this.$confirm('确定要删除这条咨询记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const result = await deleteConsultation(row.id)
        if (result.success) {
          this.$message.success('删除成功')
          this.refreshData()
        } else {
          this.$message.error(result.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.page = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadData()
    },

    // 获取状态类型
    getStatusType(status) {
      const types = {
        pending: 'warning',
        replied: 'success',
        closed: 'info'
      }
      return types[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const texts = {
        pending: '待回复',
        replied: '已回复',
        closed: '已关闭'
      }
      return texts[status] || '未知'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.consult-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .search-section {
    margin-bottom: 20px;

    .el-card {
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .stats-section {
    margin-bottom: 20px;

    .stat-card {
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }

      &.pending {
        border-left: 4px solid #e6a23c;
      }

      &.replied {
        border-left: 4px solid #67c23a;
      }

      &.closed {
        border-left: 4px solid #909399;
      }

      .stat-content {
        text-align: center;
        padding: 10px 0;

        .stat-number {
          font-size: 32px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  .table-section {
    .el-card {
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .consult-management {
    padding: 16px;

    .stats-section {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .search-section {
      .el-form {
        .el-form-item {
          display: block;
          margin-bottom: 16px;

          .el-input,
          .el-select {
            width: 100% !important;
          }
        }
      }
    }

    .table-section {
      .el-table {
        font-size: 12px;
      }

      .pagination-container {
        text-align: center;
      }
    }
  }
}
</style>
