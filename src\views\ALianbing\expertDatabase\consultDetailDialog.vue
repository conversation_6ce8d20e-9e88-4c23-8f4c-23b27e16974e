<template>
  <el-dialog v-model="visible" title="咨询详情" width="60%" class="consult-detail-dialog" top="5vh">
    <div v-if="consultation" class="detail-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <h3>基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="咨询ID">
            {{ consultation.id }}
          </el-descriptions-item>
          <el-descriptions-item label="咨询专家">
            {{ consultation.expertName }}
          </el-descriptions-item>
          <el-descriptions-item label="咨询标题">
            {{ consultation.title }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ consultation.contact }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ formatTime(consultation.submitTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(consultation.status)">
              {{ getStatusText(consultation.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 咨询内容 -->
      <div class="content-section">
        <h3>咨询内容</h3>
        <div class="content-box">
          <div v-html="consultation.content" class="rich-content"></div>
        </div>
      </div>

      <!-- 专家回复 -->
      <div v-if="consultation.reply" class="reply-section">
        <h3>专家回复</h3>
        <div class="reply-box">
          <div class="reply-meta">
            <span class="reply-time">回复时间：{{ formatTime(consultation.reply.replyTime) }}</span>
          </div>
          <div class="reply-content">
            {{ consultation.reply.content }}
          </div>
        </div>
      </div>

      <!-- 待回复状态 -->
      <div v-else-if="consultation.status === 'pending'" class="pending-section">
        <el-alert title="该咨询尚未回复" type="warning" :closable="false" show-icon>
          <template #default>
            <p>专家尚未回复此咨询，您可以点击下方按钮进行回复。</p>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="consultation && consultation.status === 'pending'"
          type="primary"
          @click="handleReply"
        >
          回复咨询
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'ConsultDetailDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    consultation: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'refresh', 'reply'],
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  methods: {
    // 关闭弹框
    handleClose() {
      this.visible = false
    },

    // 回复咨询
    handleReply() {
      // 触发父组件的回复事件
      this.$emit('reply', this.consultation)
    },

    // 获取状态类型
    getStatusType(status) {
      const types = {
        pending: 'warning',
        replied: 'success',
        closed: 'info'
      }
      return types[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const texts = {
        pending: '待回复',
        replied: '已回复',
        closed: '已关闭'
      }
      return texts[status] || '未知'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.consult-detail-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background: #fafafa;
    max-height: 70vh;
    overflow-y: auto;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    background: white;
    border-top: 1px solid #e4e7ed;
  }
}

.detail-content {
  .info-section,
  .content-section,
  .reply-section,
  .pending-section {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-left: 4px solid #409eff;
      padding-left: 12px;
    }
  }

  .content-box {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;

    .rich-content {
      line-height: 1.6;
      color: #303133;

      :deep(p) {
        margin: 0 0 12px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }

      :deep(strong) {
        font-weight: bold;
        color: #409eff;
      }

      :deep(em) {
        font-style: italic;
        color: #909399;
      }

      :deep(u) {
        text-decoration: underline;
      }

      :deep(ul),
      :deep(ol) {
        margin: 12px 0;
        padding-left: 20px;
      }

      :deep(li) {
        margin: 6px 0;
      }

      :deep(a) {
        color: #409eff;
        text-decoration: underline;

        &:hover {
          color: #66b1ff;
        }
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 12px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .reply-box {
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 8px;
    padding: 20px;

    .reply-meta {
      margin-bottom: 12px;

      .reply-time {
        font-size: 12px;
        color: #909399;
      }
    }

    .reply-content {
      line-height: 1.6;
      color: #303133;
      background: white;
      padding: 16px;
      border-radius: 6px;
      border-left: 4px solid #409eff;
    }
  }

  .pending-section {
    .el-alert {
      border-radius: 8px;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 12px;
    padding: 10px 20px;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .consult-detail-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 0 auto;
    }

    :deep(.el-dialog__body) {
      padding: 16px;
    }
  }

  .detail-content {
    .content-box,
    .reply-box {
      padding: 16px;
    }

    :deep(.el-descriptions) {
      .el-descriptions__label {
        width: 80px !important;
      }
    }
  }
}
</style>
