<template>
  <div class="expert-database">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="专家姓名">
          <el-input
            v-model="searchForm.expertName"
            placeholder="请输入专家姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @clear="handleInputClear"
          />
        </el-form-item>
        <el-form-item label="专业标签">
          <el-input
            v-model="searchForm.professionalTag"
            placeholder="请输入专业标签"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @clear="handleInputClear"
          />
        </el-form-item>
        <el-form-item label="所在地区">
          <el-input
            v-model="searchForm.location"
            placeholder="请输入所在地区"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @clear="handleInputClear"
          />
        </el-form-item>
        <el-form-item label="职称">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入职称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @clear="handleInputClear"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="searchLoading" icon="Search">
            查询
          </el-button>
          <el-button @click="handleReset" icon="Refresh"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 专家卡片展示区域 -->
    <div class="expert-cards-container">
      <!-- 搜索结果统计 -->
      <div class="search-result-info" v-if="hasSearchConditions()">
        <div class="result-count">
          <i class="el-icon-search"></i>
          搜索结果：共找到 <span class="count-number">{{ pagination.total }}</span> 位专家
        </div>
        <div class="search-conditions">
          <span v-if="searchForm.expertName" class="condition-tag">
            姓名：{{ searchForm.expertName }}
          </span>
          <span v-if="searchForm.professionalTag" class="condition-tag">
            标签：{{ searchForm.professionalTag }}
          </span>
          <span v-if="searchForm.location" class="condition-tag">
            地区：{{ searchForm.location }}
          </span>
          <span v-if="searchForm.title" class="condition-tag"> 职称：{{ searchForm.title }} </span>
        </div>
      </div>

      <div class="expert-grid" v-loading="searchLoading" element-loading-text="正在加载专家信息...">
        <div
          v-for="expert in paginatedExpertList"
          :key="expert.id"
          class="expert-card"
          @click="handleExpertClick(expert)"
        >
          <div
            class="expert-avatar"
            style="display: flex; justify-content: center; align-items: center"
          >
            <img :src="expert.avatar || defaultAvatar" :alt="expert.name" />
          </div>
          <div class="expert-info">
            <div class="expert-name"
              >{{ expert.name }} <span class="expert-work">{{ expert.work }}</span>
            </div>
            <div class="expert-tags">
              <span v-for="tag in expert.tags" :key="tag" class="expert-tag">
                {{ tag }}
              </span>
            </div>
            <div class="expert-phone"> 电话：{{ expert.phone }} </div>
            <div class="expert-bottom">
              <div class="expert-location">
                {{ expert.location }}
              </div>
              <div class="expert-actions">
                <div class="action-btn" @click.stop="handleContact(expert)">
                  <div class="chat-icon">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                  </div>
                  <div class="action-text">立即咨询</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 专家详情弹框 -->
    <ExpertDetail :visible="detailVisible" :expert="selectedExpert" @close="handleDetailClose" />
    <ExpertConsult :visible="consultVisible" :expert="selectedExpert" @close="handleConsultClose" />
  </div>
</template>

<script>
import ExpertDetail from './detail.vue'
import ExpertConsult from './consult.vue'
import { expertMockData } from './mockData.js'

export default {
  name: 'ExpertDatabase',
  components: {
    ExpertDetail,
    ExpertConsult
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        expertName: '',
        professionalTag: '',
        location: '',
        title: ''
      },
      // 分页信息
      pagination: {
        currentPage: 1,
        pageSize: 12,
        total: 0
      },
      // 专家列表
      expertList: [],
      // 搜索加载状态
      searchLoading: false,
      // 默认头像
      defaultAvatar: '/src/assets/imgs/avatar.jpg',
      // 详情弹框相关
      detailVisible: false,
      consultVisible: false,
      selectedExpert: null,
      // 模拟专家数据
      mockExpertData: expertMockData
    }
  },
  computed: {
    // 分页显示的专家列表
    paginatedExpertList() {
      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize
      const end = start + this.pagination.pageSize
      return this.expertList.slice(start, end)
    }
  },
  mounted() {
    this.loadExpertData()
  },
  methods: {
    // 加载专家数据
    loadExpertData() {
      // 模拟API调用
      setTimeout(() => {
        // 如果有搜索条件，先执行搜索
        if (this.hasSearchConditions()) {
          this.handleSearch()
        } else {
          // 没有搜索条件时显示所有数据
          this.expertList = this.mockExpertData
          this.pagination.total = this.mockExpertData.length
        }
      }, 300)
    },

    // 检查是否有搜索条件
    hasSearchConditions() {
      return (
        this.searchForm.expertName ||
        this.searchForm.professionalTag ||
        this.searchForm.location ||
        this.searchForm.title
      )
    },

    // 搜索处理
    handleSearch() {
      console.log('搜索条件:', this.searchForm)

      // 设置加载状态
      this.searchLoading = true

      // 模拟API调用延迟
      setTimeout(() => {
        // 重置分页到第一页
        this.pagination.currentPage = 1

        // 根据搜索条件过滤专家数据
        let filteredData = this.mockExpertData

        // 按专家姓名搜索
        if (this.searchForm.expertName) {
          filteredData = filteredData.filter((expert) =>
            expert.name.toLowerCase().includes(this.searchForm.expertName.toLowerCase())
          )
        }

        // 按专业标签搜索
        if (this.searchForm.professionalTag) {
          filteredData = filteredData.filter((expert) =>
            expert.tags.some((tag) =>
              tag.toLowerCase().includes(this.searchForm.professionalTag.toLowerCase())
            )
          )
        }

        // 按所在地区搜索
        if (this.searchForm.location) {
          filteredData = filteredData.filter((expert) =>
            expert.location.toLowerCase().includes(this.searchForm.location.toLowerCase())
          )
        }

        // 按职称搜索
        if (this.searchForm.title) {
          filteredData = filteredData.filter((expert) =>
            expert.work.toLowerCase().includes(this.searchForm.title.toLowerCase())
          )
        }

        // 更新专家列表和分页信息
        this.expertList = filteredData
        this.pagination.total = filteredData.length

        // 关闭加载状态
        this.searchLoading = false

        // 显示搜索结果提示
        if (filteredData.length === 0) {
          this.$message.warning('未找到符合条件的专家')
        } else {
          this.$message.success(`找到 ${filteredData.length} 位专家`)
        }
      }, 500) // 模拟网络延迟
    },

    // 重置搜索
    handleReset() {
      // 清空搜索表单
      this.searchForm = {
        expertName: '',
        professionalTag: '',
        location: '',
        title: ''
      }

      // 重置分页
      this.pagination.currentPage = 1
      this.pagination.pageSize = 12

      // 重新加载所有数据
      this.expertList = this.mockExpertData
      this.pagination.total = this.mockExpertData.length

      this.$message.success('已重置搜索条件')
    },

    // 输入框清空处理
    handleInputClear() {
      // 当输入框被清空时，如果没有其他搜索条件，则显示所有数据
      this.$nextTick(() => {
        if (!this.hasSearchConditions()) {
          this.expertList = this.mockExpertData
          this.pagination.total = this.mockExpertData.length
          this.pagination.currentPage = 1
        }
      })
    },

    // 专家卡片点击
    handleExpertClick(expert) {
      console.log('点击专家:', expert)
      this.selectedExpert = expert
      this.detailVisible = true
    },

    // 关闭详情弹框
    handleDetailClose() {
      this.detailVisible = false
      this.selectedExpert = null
    },

    handleConsultClose() {
      this.consultVisible = false
      this.selectedExpert = null
    },

    // 联系专家
    handleContact(expert) {
      this.selectedExpert = expert
      this.consultVisible = true
      this.$message.success(`正在联系 ${expert.name}`)
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      // 如果有搜索条件，重新搜索；否则重新加载数据
      if (this.hasSearchConditions()) {
        this.handleSearch()
      } else {
        this.loadExpertData()
      }
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      // 分页切换时不需要重新加载数据，因为我们已经有了完整的搜索结果
      // 这里可以添加真实分页逻辑，目前使用前端分页
    }
  }
}
</script>

<style lang="scss" scoped>
.expert-database {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .search-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 20px;

        :deep(.el-form-item__label) {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  .expert-cards-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-result-info {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 15px;
      margin-bottom: 20px;

      .result-count {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 10px;

        i {
          color: #4285f4;
          margin-right: 8px;
        }

        .count-number {
          color: #4285f4;
          font-weight: 600;
        }
      }

      .search-conditions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .condition-tag {
          background: #e8f4fd;
          color: #4285f4;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          border: 1px solid #d1e9ff;
        }
      }
    }

    .expert-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
      margin-bottom: 30px;

      .expert-card {
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
          border-color: #4285f4;
        }

        .expert-avatar {
          text-align: center;
          margin-bottom: 15px;

          img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #f0f0f0;
          }
        }

        .expert-info {
          text-align: center;

          .expert-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          .expert-work {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            font-weight: 500;
          }

          .expert-tags {
            margin-bottom: 12px;

            .expert-tag {
              display: inline-block;
              background: #e8f4fd;
              color: #4285f4;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              margin: 2px 4px;
              border: 1px solid #d1e9ff;
            }
          }

          .expert-phone {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
          }

          .expert-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
          }

          .expert-location {
            color: #e74c3c;
            font-size: 13px;
            background: #f8f9fa;
            padding: 6px 12px;
            border-radius: 15px;
            display: inline-block;
          }

          .expert-actions {
            .action-btn {
              display: flex;
              flex-direction: column;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;

              .chat-icon {
                background: #52c41a;
                width: 50px;
                height: 30px;
                border-radius: 25px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 4px;
                margin-bottom: 6px;
                transition: all 0.3s ease;

                .dot {
                  width: 6px;
                  height: 6px;
                  background: white;
                  border-radius: 50%;
                }
              }

              .action-text {
                color: #333;
                font-size: 12px;
                font-weight: 500;
              }

              &:hover {
                .chat-icon {
                  background: #389e0d;
                  transform: scale(1.05);
                }

                .action-text {
                  color: #52c41a;
                }
              }
            }
          }
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;

      :deep(.el-pagination) {
        .el-pager li {
          &.is-active {
            background-color: #4285f4;
            color: white;
          }
        }

        .btn-next,
        .btn-prev {
          &:hover {
            color: #4285f4;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .expert-database {
    padding: 10px;

    .search-container {
      padding: 15px;

      .search-form {
        .el-form-item {
          margin-right: 0;
          width: 100%;

          :deep(.el-input) {
            width: 100% !important;
          }
        }
      }
    }

    .expert-cards-container {
      padding: 15px;

      .expert-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }
    }
  }
}
</style>
