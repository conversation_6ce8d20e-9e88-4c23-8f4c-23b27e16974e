<template>
  <el-dialog
    v-model="consultVisible"
    :title="`${expert?.name || '专家'} ${expert?.work || ''}-咨询留言`"
    width="50%"
    :before-close="handleClose"
    class="expert-consult-dialog"
    top="5vh"
  >
    <div class="consult-form">
      <el-form
        ref="consultFormRef"
        :model="consultForm"
        :rules="consultRules"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="咨询标题：" prop="title">
          <el-input
            v-model="consultForm.title"
            placeholder="请输入咨询标题"
            maxlength="50"
            show-word-limit
            clearable
          />
        </el-form-item>

        <el-form-item label="咨询人联系方式：" prop="contact">
          <el-input
            v-model="consultForm.contact"
            placeholder="请输入联系方式"
            maxlength="20"
            clearable
          />
        </el-form-item>

        <el-form-item label="留言内容：" prop="content">
          <div class="content-editor">
            <!-- 工具栏 -->
            <div class="editor-toolbar">
              <el-button-group>
                <el-button size="small" @click="insertFormat('bold')" title="加粗">
                  <el-icon><strong>B</strong></el-icon>
                </el-button>
                <el-button size="small" @click="insertFormat('italic')" title="斜体">
                  <el-icon><em>I</em></el-icon>
                </el-button>
                <el-button size="small" @click="insertFormat('underline')" title="下划线">
                  <el-icon><u>U</u></el-icon>
                </el-button>
              </el-button-group>

              <el-button-group style="margin-left: 10px">
                <el-button size="small" @click="insertList('ul')" title="无序列表">
                  <el-icon>•</el-icon>
                </el-button>
                <el-button size="small" @click="insertList('ol')" title="有序列表">
                  <el-icon>1.</el-icon>
                </el-button>
              </el-button-group>

              <el-button-group style="margin-left: 10px">
                <el-button size="small" @click="insertLink" title="插入链接">
                  <el-icon>🔗</el-icon>
                </el-button>
                <el-button size="small" @click="insertImage" title="插入图片">
                  <el-icon>📷</el-icon>
                </el-button>
              </el-button-group>
            </div>

            <!-- 编辑器 -->
            <div
              ref="editorRef"
              class="rich-editor"
              contenteditable="true"
              @input="handleContentChange"
              @paste="handlePaste"
              placeholder="请输入内容"
            ></div>

            <!-- 字数统计 -->
            <div class="word-count"> {{ contentLength }}/1000 </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { saveConsultation } from './consultDatabase.js'

export default {
  name: 'ExpertConsult',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    expert: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      consultForm: {
        title: '',
        contact: '',
        content: ''
      },
      consultRules: {
        title: [
          { required: true, message: '请输入咨询标题', trigger: 'blur' },
          { min: 2, max: 50, message: '标题长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        contact: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          {
            pattern: /^(1[3-9]\d{9}|[\w\.-]+@[\w\.-]+\.\w+)$/,
            message: '请输入正确的手机号或邮箱',
            trigger: 'blur'
          }
        ],
        content: [
          { required: true, message: '请输入留言内容', trigger: 'blur' },
          { min: 10, message: '留言内容至少10个字符', trigger: 'blur' }
        ]
      },
      submitting: false,
      contentLength: 0
    }
  },
  computed: {
    consultVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('close')
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.consultForm = {
        title: '',
        contact: '',
        content: ''
      }
      this.contentLength = 0
      if (this.$refs.editorRef) {
        this.$refs.editorRef.innerHTML = ''
      }
      if (this.$refs.consultFormRef) {
        this.$refs.consultFormRef.clearValidate()
      }
    },

    // 处理内容变化
    handleContentChange() {
      const editor = this.$refs.editorRef
      if (editor) {
        this.consultForm.content = editor.innerHTML
        this.contentLength = editor.innerText.length

        // 限制字数
        if (this.contentLength > 1000) {
          const text = editor.innerText.substring(0, 1000)
          editor.innerText = text
          this.consultForm.content = text
          this.contentLength = 1000
        }
      }
    },

    // 处理粘贴
    handlePaste(e) {
      e.preventDefault()
      const text = (e.clipboardData || window.clipboardData).getData('text')
      document.execCommand('insertText', false, text)
    },

    // 插入格式
    insertFormat(command) {
      document.execCommand(command, false, null)
      this.$refs.editorRef.focus()
    },

    // 插入列表
    insertList(type) {
      const command = type === 'ul' ? 'insertUnorderedList' : 'insertOrderedList'
      document.execCommand(command, false, null)
      this.$refs.editorRef.focus()
    },

    // 插入链接
    insertLink() {
      const url = prompt('请输入链接地址:')
      if (url) {
        document.execCommand('createLink', false, url)
      }
      this.$refs.editorRef.focus()
    },

    // 插入图片
    insertImage() {
      const url = prompt('请输入图片地址:')
      if (url) {
        document.execCommand('insertImage', false, url)
      }
      this.$refs.editorRef.focus()
    },

    // 提交表单
    async handleSubmit() {
      try {
        // 验证表单
        await this.$refs.consultFormRef.validate()

        this.submitting = true

        // 构造提交数据
        const consultData = {
          expertId: this.expert?.id,
          expertName: this.expert?.name,
          title: this.consultForm.title,
          contact: this.consultForm.contact,
          content: this.consultForm.content,
          contentText: this.$refs.editorRef.innerText, // 纯文本内容
          submitTime: new Date().toISOString(),
          status: 'pending' // pending, replied, closed
        }

        // 保存到模拟数据库
        const result = await saveConsultation(consultData)

        if (result.success) {
          this.$message.success('咨询提交成功！专家会尽快回复您。')
          this.handleClose()
        } else {
          this.$message.error('提交失败，请重试')
        }
      } catch (error) {
        console.error('提交咨询失败:', error)
        this.$message.error('提交失败，请检查填写内容')
      } finally {
        this.submitting = false
      }
    },

    // 关闭弹框
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.expert-consult-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background: #fafafa;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    background: white;
    border-top: 1px solid #e4e7ed;
  }
}

.consult-form {
  .el-form {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .el-form-item {
    margin-bottom: 24px;

    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #303133;
    }
  }

  .content-editor {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;

    &:focus-within {
      border-color: #409eff;
    }

    .editor-toolbar {
      background: #f5f7fa;
      padding: 8px 12px;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      align-items: center;

      .el-button-group {
        .el-button {
          padding: 4px 8px;
          font-size: 12px;

          &:hover {
            background: #409eff;
            color: white;
          }
        }
      }
    }

    .rich-editor {
      min-height: 200px;
      max-height: 400px;
      padding: 12px;
      overflow-y: auto;
      line-height: 1.6;
      font-size: 14px;

      &:focus {
        outline: none;
      }

      &[contenteditable]:empty::before {
        content: attr(placeholder);
        color: #c0c4cc;
        font-style: italic;
      }

      // 富文本内容样式
      :deep(strong) {
        font-weight: bold;
      }

      :deep(em) {
        font-style: italic;
      }

      :deep(u) {
        text-decoration: underline;
      }

      :deep(ul),
      :deep(ol) {
        margin: 8px 0;
        padding-left: 20px;
      }

      :deep(li) {
        margin: 4px 0;
      }

      :deep(a) {
        color: #409eff;
        text-decoration: underline;

        &:hover {
          color: #66b1ff;
        }
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 8px 0;
      }
    }

    .word-count {
      padding: 8px 12px;
      background: #f5f7fa;
      border-top: 1px solid #e4e7ed;
      text-align: right;
      font-size: 12px;
      color: #909399;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 12px;
    padding: 10px 20px;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .expert-consult-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 0 auto;
    }

    :deep(.el-dialog__body) {
      padding: 16px;
    }
  }

  .consult-form {
    .el-form {
      padding: 16px;
    }

    .content-editor {
      .editor-toolbar {
        flex-wrap: wrap;
        gap: 8px;

        .el-button-group {
          margin-bottom: 4px;
        }
      }

      .rich-editor {
        min-height: 150px;
      }
    }
  }
}
</style>
