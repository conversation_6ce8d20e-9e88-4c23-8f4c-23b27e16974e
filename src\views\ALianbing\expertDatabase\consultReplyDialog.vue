<template>
  <el-dialog
    v-model="visible"
    title="回复咨询"
    width="50%"
    class="consult-reply-dialog"
    top="5vh"
  >
    <div v-if="consultation" class="reply-content">
      <!-- 咨询信息概览 -->
      <div class="consult-overview">
        <h3>咨询信息</h3>
        <div class="overview-card">
          <div class="overview-item">
            <span class="label">标题：</span>
            <span class="value">{{ consultation.title }}</span>
          </div>
          <div class="overview-item">
            <span class="label">咨询人：</span>
            <span class="value">{{ consultation.contact }}</span>
          </div>
          <div class="overview-item">
            <span class="label">提交时间：</span>
            <span class="value">{{ formatTime(consultation.submitTime) }}</span>
          </div>
        </div>
        
        <!-- 咨询内容 -->
        <div class="consult-content">
          <h4>咨询内容：</h4>
          <div class="content-box">
            <div v-html="consultation.content" class="rich-content"></div>
          </div>
        </div>
      </div>

      <!-- 回复表单 -->
      <div class="reply-form">
        <h3>专家回复</h3>
        <el-form
          ref="replyFormRef"
          :model="replyForm"
          :rules="replyRules"
          label-width="80px"
        >
          <el-form-item label="回复内容" prop="content">
            <el-input
              v-model="replyForm.content"
              type="textarea"
              :rows="8"
              placeholder="请输入回复内容..."
              maxlength="2000"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          发送回复
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { replyConsultation } from './consultDatabase.js'

export default {
  name: 'ConsultReplyDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    consultation: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'refresh'],
  data() {
    return {
      replyForm: {
        content: ''
      },
      replyRules: {
        content: [
          { required: true, message: '请输入回复内容', trigger: 'blur' },
          { min: 10, message: '回复内容至少10个字符', trigger: 'blur' },
          { max: 2000, message: '回复内容不能超过2000个字符', trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.replyForm = {
        content: ''
      }
      if (this.$refs.replyFormRef) {
        this.$refs.replyFormRef.clearValidate()
      }
    },

    // 提交回复
    async handleSubmit() {
      try {
        // 验证表单
        await this.$refs.replyFormRef.validate()
        
        this.submitting = true
        
        // 提交回复
        const result = await replyConsultation(this.consultation.id, this.replyForm.content)
        
        if (result.success) {
          this.$message.success('回复发送成功！')
          this.$emit('refresh')
          this.handleClose()
        } else {
          this.$message.error(result.message || '回复失败，请重试')
        }
      } catch (error) {
        console.error('回复失败:', error)
        this.$message.error('回复失败，请检查填写内容')
      } finally {
        this.submitting = false
      }
    },

    // 关闭弹框
    handleClose() {
      this.visible = false
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.consult-reply-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    color: white;
    padding: 20px 24px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
    
    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 20px;
        
        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 24px;
    background: #fafafa;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    background: white;
    border-top: 1px solid #e4e7ed;
  }
}

.reply-content {
  .consult-overview,
  .reply-form {
    margin-bottom: 24px;
    
    h3 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-left: 4px solid #67c23a;
      padding-left: 12px;
    }
    
    h4 {
      margin: 16px 0 8px 0;
      color: #606266;
      font-size: 14px;
      font-weight: 600;
    }
  }
  
  .overview-card {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    
    .overview-item {
      display: flex;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        width: 80px;
        color: #909399;
        font-size: 14px;
        flex-shrink: 0;
      }
      
      .value {
        color: #303133;
        font-size: 14px;
        flex: 1;
      }
    }
  }
  
  .content-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    
    .rich-content {
      line-height: 1.6;
      color: #495057;
      font-size: 14px;
      
      :deep(p) {
        margin: 0 0 8px 0;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      :deep(strong) {
        font-weight: bold;
        color: #409eff;
      }
      
      :deep(em) {
        font-style: italic;
        color: #909399;
      }
      
      :deep(u) {
        text-decoration: underline;
      }
      
      :deep(ul), :deep(ol) {
        margin: 8px 0;
        padding-left: 20px;
      }
      
      :deep(li) {
        margin: 4px 0;
      }
      
      :deep(a) {
        color: #409eff;
        text-decoration: underline;
        
        &:hover {
          color: #66b1ff;
        }
      }
    }
  }
  
  .reply-form {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    
    .el-form-item {
      margin-bottom: 0;
      
      :deep(.el-form-item__label) {
        font-weight: 600;
        color: #303133;
      }
      
      :deep(.el-textarea__inner) {
        border-radius: 6px;
        font-family: inherit;
        line-height: 1.6;
        
        &:focus {
          border-color: #67c23a;
          box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 12px;
    padding: 10px 20px;
    
    &.el-button--primary {
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #5daf34 0%, #7bc143 100%);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .consult-reply-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 0 auto;
    }
    
    :deep(.el-dialog__body) {
      padding: 16px;
    }
  }
  
  .reply-content {
    .overview-card,
    .reply-form {
      padding: 16px;
    }
    
    .overview-card {
      .overview-item {
        flex-direction: column;
        
        .label {
          width: auto;
          margin-bottom: 4px;
        }
      }
    }
  }
}
</style>
