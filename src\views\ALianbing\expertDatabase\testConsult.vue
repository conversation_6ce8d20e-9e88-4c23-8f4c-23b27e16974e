<template>
  <div class="test-consult">
    <h2>咨询功能测试页面</h2>
    
    <div class="test-section">
      <h3>1. 测试咨询弹框</h3>
      <el-button type="primary" @click="openConsultDialog">
        打开咨询弹框
      </el-button>
    </div>

    <div class="test-section">
      <h3>2. 查看咨询管理</h3>
      <el-button type="success" @click="openManagement">
        打开咨询管理
      </el-button>
    </div>

    <div class="test-section">
      <h3>3. 查看存储的咨询数据</h3>
      <el-button type="info" @click="showStoredData">
        显示本地存储数据
      </el-button>
      
      <div v-if="storedData.length > 0" class="data-display">
        <h4>本地存储的咨询数据：</h4>
        <el-table :data="storedData" border style="width: 100%; margin-top: 16px;">
          <el-table-column prop="id" label="ID" width="60" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="expertName" label="专家" width="100" />
          <el-table-column prop="contact" label="联系方式" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submitTime" label="提交时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.submitTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 咨询弹框 -->
    <ExpertConsult 
      :visible="consultVisible" 
      :expert="testExpert" 
      @close="handleConsultClose" 
    />
  </div>
</template>

<script>
import ExpertConsult from './consult.vue'

export default {
  name: 'TestConsult',
  components: {
    ExpertConsult
  },
  data() {
    return {
      consultVisible: false,
      storedData: [],
      testExpert: {
        id: 1,
        name: '王强',
        work: '教授',
        department: '河北农业大学',
        tags: ['小麦', '玉米'],
        phone: '18356980126',
        location: '河北农业大学',
        email: '<EMAIL>'
      }
    }
  },
  methods: {
    // 打开咨询弹框
    openConsultDialog() {
      this.consultVisible = true
    },

    // 关闭咨询弹框
    handleConsultClose() {
      this.consultVisible = false
      // 关闭后刷新数据显示
      this.showStoredData()
    },

    // 打开咨询管理
    openManagement() {
      // 这里可以跳转到咨询管理页面
      this.$router.push('/expert-consult-management')
    },

    // 显示本地存储数据
    showStoredData() {
      const stored = localStorage.getItem('expertConsultations')
      if (stored) {
        try {
          this.storedData = JSON.parse(stored)
        } catch (error) {
          console.error('解析存储数据失败:', error)
          this.storedData = []
        }
      } else {
        this.storedData = []
      }
    },

    // 获取状态类型
    getStatusType(status) {
      const types = {
        pending: 'warning',
        replied: 'success',
        closed: 'info'
      }
      return types[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const texts = {
        pending: '待回复',
        replied: '已回复',
        closed: '已关闭'
      }
      return texts[status] || '未知'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  },
  mounted() {
    this.showStoredData()
  }
}
</script>

<style lang="scss" scoped>
.test-consult {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #303133;
    margin-bottom: 30px;
    text-align: center;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: white;

    h3 {
      color: #409eff;
      margin-bottom: 16px;
      font-size: 16px;
    }

    .el-button {
      margin-right: 12px;
    }

    .data-display {
      margin-top: 20px;

      h4 {
        color: #606266;
        margin-bottom: 12px;
      }
    }
  }
}
</style>
